package com.hailiang.composition.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.PointerEventType
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.AiStreamReason
import com.hailiang.hlutil.HLog
import com.hailiang.markdown.MarkDownCommentView
import com.hailiang.xxb.composition.R

@Composable
fun AiSteamLayout(aiStreamDetail: AiStreamDetail?, doRetry: () -> Unit) {
    aiStreamDetail?.let {
        if (it.answering.isNullOrEmpty()) {
            AiReasoningLayout(
                content = it.reasoning,
                finishReason = aiStreamDetail.getAiStreamReason(),
                retry = doRetry
            )
        } else {
            AiAnsweringLayout(
                content = it.answering,
                finishReason = aiStreamDetail.getAiStreamReason(),
                retry = doRetry
            )
        }
    }
}

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/8 10:54
 */
@Composable
private fun AiReasoningLayout(content: String?, finishReason: AiStreamReason?, retry: () -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(shape = RoundedCornerShape(CORNER_RADIUS))
            .background(color = Color.White)
            .padding(16.dp)
    ) {
        StreamScrollColumn(key1 = content, key2 = finishReason) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                AiThinkingAnimation()
                Spacer(modifier = Modifier.size(2.dp))
                Text(text = "思考中...", color = Color(0xFF5565FF), fontSize = 18.sp)
            }
            Spacer(modifier = Modifier.size(12.dp))
            Text(
                text = content ?: "", fontSize = 16.sp, color = Color.Black.copy(
                    alpha = 0.7F
                )
            )
            StreamRetryText(finishReason = finishReason, retry)
        }
    }
}

@Composable
private fun AiAnsweringLayout(content: String?, finishReason: AiStreamReason?, retry: () -> Unit) {
    StreamScrollColumn(key1 = content, key2 = finishReason) {
        Box(modifier = Modifier.fillMaxWidth()) {
            AndroidView(
                modifier = Modifier.wrapContentSize(),
                factory = { context ->
                    HLog.i("AiReasoningLayout", "AndroidView: factory")
                    MarkDownCommentView(context).apply {
                        id = R.id.ai_answering_tv
                        setPadding(0, 0, 0, 0)
                        setEditMode(false)
                        alpha = 0.7F
                    }
                },
                update = { view ->
                    view.setTextContent(content ?: "")
                }
            )
        }
        StreamRetryText(finishReason = finishReason, retry)
    }
}

@Composable
private fun StreamScrollColumn(
    key1: Any?,
    key2: Any?,
    content: @Composable ColumnScope.() -> Unit,
) {
    var isUserScrolled by remember { mutableStateOf(false) }
    val scrollState = rememberScrollState()
    LaunchedEffect(key1, key2) {
        if (!isUserScrolled) {
            scrollState.animateScrollTo(scrollState.maxValue)
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .clip(shape = RoundedCornerShape(CORNER_RADIUS))
            .background(color = Color(0xFFF5F6FD))
            .padding(horizontal = 20.dp, vertical = 16.dp)
            .verticalScroll(state = scrollState)
            .pointerInput(Unit) {
                awaitPointerEventScope {
                    while (true) {
                        val event = awaitPointerEvent()
                        if (event.type == PointerEventType.Release) {
                            isUserScrolled = (scrollState.maxValue - scrollState.value) > 50
//                            HLog.i("StreamScrollColumn", "PointerEvent Release: scroll offset${(scrollState.maxValue - scrollState.value)}")
                        }
                    }
                }
            },
        content = content
    )
}

@Composable
private fun StreamRetryText(finishReason: AiStreamReason?, retry: () -> Unit) {
    when {
        finishReason?.isStreamError() == true -> {
            RetryText(preText = "服务异常，请", retry)
        }

        finishReason?.isNetworkError() == true -> {
            RetryText(preText = "网络异常，请", retry)
        }
    }
}

@Composable
private fun RetryText(preText: String, retry: () -> Unit) {
    Spacer(modifier = Modifier.size(26.dp))
    val annotatedText = buildAnnotatedString {
        append(preText)
        // 添加可点击部分
        pushStringAnnotation(
            tag = "click", // 标识符
            annotation = "clickable_1" // 点击时的数据
        )
        withStyle(
            style = SpanStyle(
                color = Color(0xFF5565FF),
                textDecoration = TextDecoration.None
            )
        ) {
            append("重试")
        }
        pop()
    }
    ClickableText(
        text = annotatedText,
        style = TextStyle(
            color = Color.Black,
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold
        ),
        onClick = { offset ->
            annotatedText.getStringAnnotations(
                tag = "click",
                start = offset,
                end = offset
            ).firstOrNull()?.let { annotation ->
                println("onClick: $annotation")
                retry()
            }
        }
    )
    Spacer(modifier = Modifier.size(26.dp))
}

@Preview
@Composable
private fun AiReasoningLayoutPreview() {
    AiReasoningLayout(
        "AiReasoningLayoutPreviewAiReasoningLayoutPreviewAiReasoningLayoutPreviewAiReasoningLayoutPreviewAiReasoningLayoutPreview",
        AiStreamReason.NetworkError,
        retry = {})
}

@Preview
@Composable
private fun AiAnsweringLayoutPreview() {
    AiAnsweringLayout(
        "AiAnsweringLayoutPreviewAiAnsweringLayoutPreviewAiAnsweringLayoutPreviewAiAnsweringLayoutPreview",
        AiStreamReason.Error,
        retry = {})
}
