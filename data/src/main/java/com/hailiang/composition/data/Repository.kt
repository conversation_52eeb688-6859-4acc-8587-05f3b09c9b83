package com.hailiang.composition.data

import com.alibaba.fastjson2.JSON
import com.alibaba.fastjson2.JSONObject
import com.hailiang.composition.asPo
import com.hailiang.composition.asVo
import com.hailiang.composition.data.bean.AiGuidance
import com.hailiang.composition.data.bean.AiStreamChunkResponse
import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.AiStreamStatus
import com.hailiang.composition.data.bean.CompositionCheckBean
import com.hailiang.composition.data.bean.CompositionCheckBean.CheckContent
import com.hailiang.composition.data.bean.CompositionStatusResponse
import com.hailiang.composition.data.bean.CreateWorkBean
import com.hailiang.composition.data.bean.PictureAnswerInfo
import com.hailiang.composition.data.bean.StudentAnswerInfo
import com.hailiang.composition.data.bean.TaskBean
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.bean.WorkStatus
import com.hailiang.composition.data.bean.request.ArticleType
import com.hailiang.composition.data.bean.request.CompositionSecondSubmitRequest
import com.hailiang.composition.data.bean.request.EvaluateType
import com.hailiang.composition.data.bean.request.FeedbackEvaluateRequest
import com.hailiang.composition.data.bean.request.FeedbackType
import com.hailiang.composition.data.bean.response.FirstSubmitResponse
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.data.source.CompositionLocalDataSource
import com.hailiang.composition.data.source.CompositionRemoteDataSource
import com.hailiang.composition.data.source.WorkLocalDataSource
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.JsonUtil
import com.hailiang.hlutil.date.DateUtil
import com.hailiang.hlutil.ext.jsonToList
import com.hailiang.hlutil.parseJson
import com.hailiang.libhttp.BaseHttpResult
import com.hailiang.ui.designsystem.toast.ToastUtils
import com.hailiang.workcloud.data.vo.CompositionCheckDetail
import com.hailiang.workcloud.data.vo.CompositionPractice
import com.hailiang.workcloud.database.room.pojo.CompositionCheckDetailPo
import com.hailiang.workcloud.database.room.pojo.CompositionPracticePo
import com.hailiang.workcloud.database.room.pojo.CompositionWorkAnswerPo
import kotlinx.coroutines.flow.Flow

class Repository {
    companion object {
        private val feedbackMap = HashMap<String, String?>()
    }

    private val remoteDataSource by lazy {
        CompositionRemoteDataSource()
    }
    private val compositionLocalDataSource by lazy {
        CompositionLocalDataSource()
    }
    private val workLocalDataSource by lazy {
        WorkLocalDataSource()
    }

    /**
     * 请求作文信息
     */
    suspend fun requestCompositionInfo(
        workId: Long,
        workStateId: Long,
    ): WorkDetail? {
        val result = remoteDataSource.requestCompositionInfo(workId = workId)
        if (!result.isSuccess) {
            HLog.i(HTag.TAG, "获取作文信息失败!(${result.message})")
            return null
        }
        val data = result.data
//        HLog.i(HTag.TAG, "获取到作文信息: $data")
        updateWorkStateWithRemote(data)
        val studentAnswerInfo = data.studentAnswerInfo ?: StudentAnswerInfo()
        studentAnswerInfo.imgInfoList = data.imgInfoList?.mapNotNull {
            // 图片的更新时间是相同的，所以直接赋值即可
            studentAnswerInfo.imageUpdateTime = it.createTime
            it.answerPicUrl
        }
        // 处理变更平板时，提交图片发生变化的情况。
        checkStudentImageChanged(
            workId = workId,
            workStateId = workStateId,
            studentAnswerInfo = studentAnswerInfo,
            remoteWorkStatus = data.schoolworkStateInfo?.status ?: WorkStatus.UN_DO
        )
        // 更新学生作答情况
        updateCompositionWorkAnswer(
            workId = workId,
            workStateId = workStateId,
            studentAnswerInfo = studentAnswerInfo,
        )
        val secondAnswerParagraphs = parseJson<List<String>>(
            studentAnswerInfo.studentSecondAnswerDetail,
            listOf()
        )
        data.teacherCheckInfo?.let {
            updateCheckInfo(
                workId = workId,
                workStateId = workStateId,
                checkUserId = 1,
                secondAnswerParagraphs?.firstOrNull(),
                JSON.toJSONString(secondAnswerParagraphs?.drop(1)),
                checkContent = it.checkContent
            )
        }
        data.feedbackInfoList?.forEach {
            updateFeedbackEvaluate(
                workId = workId,
                feedbackType = it.feedbackType,
                articleType = it.articleType,
                userType = it.userType,
                evaluateType = it.evaluateType
            )
        }
        return data
    }

    private fun updateWorkStateWithRemote(workDetail: WorkDetail?) {
        val workInfo = workDetail?.schoolworkInfo ?: return
        val workStateInfo = workDetail.schoolworkStateInfo ?: return
        val newState = workStateInfo.status
        val localState = workLocalDataSource.findOrCreateWorkState(
            workId = workInfo.id,
            workStateId = workStateInfo.id
        )
        when {
            newState >= WorkStatus.ALL_SUBMITTED || localState.state == WorkStatus.UN_DO -> {
                // 全部提交，或者本地未作，忽略本地状态
                workLocalDataSource.updateWorkState(
                    localState.copy(state = newState)
                )
            }

            localState.state < WorkStatus.ALL_SUBMITTED -> { // 做题中，忽略远程状态
            }

            else -> {
                workLocalDataSource.updateWorkState(
                    localState.copy(state = newState)
                )
            }
        }
    }

    /**
     * 处理变更平板时，提交图片发生变化的情况。
     * 1. 清空本地临时作答图片，使用远程
     * 2. 作业状态已远程为准
     */
    private fun checkStudentImageChanged(
        workId: Long,
        workStateId: Long,
        studentAnswerInfo: StudentAnswerInfo,
        remoteWorkStatus: Int,
    ) {
        val local = compositionLocalDataSource.queryCompositionWorkAnswer(workId)
        val remoteImageTime = studentAnswerInfo.imageUpdateTime
        if (!remoteImageTime.isNullOrEmpty() && remoteImageTime != local?.imageUpdateTime) {
            // 时间发生变化 需要清除本地的临时作答图片
            HLog.i(
                HTag.TAG,
                "图片提交时间发生变化 需要清除本地的临时作答图片，替换为远程数据: workId: $workId; $remoteImageTime"
            )
            saveTempAnswerPictures(
                workId = workId,
                pictureAnswerInfo = studentAnswerInfo.imgInfoList?.mapIndexed { index, s ->
                    PictureAnswerInfo().also { pictureAnswerInfo ->
                        pictureAnswerInfo.workId = workId
                        pictureAnswerInfo.path = null
                        pictureAnswerInfo.url = s
                        pictureAnswerInfo.index = index
                    }
                })
            //
//            workLocalDataSource.updateDoWorkState(
//                workId = workId, workStateId = workStateId, newState = remoteWorkStatus
//            )
        }
    }

    suspend fun requestAiCheckInfo(
        workId: Long,
        workStateId: Long,
        isSubmitted: Boolean,
    ): CheckContent? {
        return if (isSubmitted) { // 已提交获取AI教师二稿批改详情
            requestCompositionInfo(
                workId = workId,
                workStateId = workStateId
            )?.teacherCheckInfo?.checkContent?.copy(
                isSubmitted = isSubmitted
            )
        } else {
            requestStudentAiCheckInfo(
                workId = workId,
                workStateId = workStateId
            )?.checkContent
        }
    }

    /**
     * 获取学生AI批改结果
     * 包含 OCR 和 点拨
     */
    private suspend fun requestStudentAiCheckInfo(
        workId: Long,
        workStateId: Long,
    ): CompositionCheckBean? {
        val result = remoteDataSource.requestStudentAiCorrectInfo(workStateId)
        val aiCheckContent = result.data?.checkContent
        if (!result.isSuccess || aiCheckContent == null) {
            HLog.i(HTag.TAG, "获取Ai批改结果失败!(${result.message})")
            return null
        }
        val orcJobStatus = aiCheckContent.getOcrJobStatus()
        val jobStatus = aiCheckContent.getJobStatus()
        updateCheckInfo(
            workId = workId,
            workStateId = workStateId,
            checkUserId = 0,
            aiCheckContent.ocrResult?.content?.firstOrNull(),
            JSON.toJSONString(aiCheckContent.ocrResult?.content?.drop(1)),
            checkContent = aiCheckContent
        )
        when {
            jobStatus == JobStatus.FAILED -> {
                HLog.i(HTag.TAG, "识别任务失败，删除本地历史数据：ocr, 点拨等")
                if (aiCheckContent.message.isNullOrEmpty()) { // 添加一个默认错误信息
                    aiCheckContent.message = "作文识别失败，请重新提交!"
                }
                compositionLocalDataSource.deleteCompositionPractice(
                    workId = workId, workStateId = workStateId
                )
                //
                updateTempAnswerPicturesWhenAiFailed(workId)
            }

            orcJobStatus.isSuccess() -> { // OCR成功
                if (orcJobStatus.isSuccess()) {
                    // 清空之前的异常信息记录
//                    workLocalDataSource.clearWorkErrorMessage(
//                        workId = workId,
//                        workStateId = workStateId,
//                    )
                    val ocrTitle = aiCheckContent.ocrResult?.content?.firstOrNull()
                    val ocrContent = JsonUtil.objToJson(aiCheckContent.ocrResult?.content?.drop(1))
                    val compositionPractice = compositionLocalDataSource.queryCompositionPractice(
                        workId = workId, workStateId = workStateId
                    )
                    compositionLocalDataSource.updateCompositionPractice(
                        CompositionPracticePo(
                            id = compositionPractice?.id,
                            workId = workId,
                            workStateId = workStateId,
                            secondPracticeTitle = compositionPractice?.secondPracticeTitle
                                ?: ocrTitle,
                            secondPracticeContent = compositionPractice?.secondPracticeContent
                                ?: ocrContent,
                        )
                    )
                }
            }

            else -> { // 成功
            }
        }
        return result.data
    }

    /**
     * 更新议论文批改结果
     */
    private fun updateCheckInfo(
        workId: Long,
        workStateId: Long,
        checkUserId: Long,
        compositionTitle: String?,
        compositionContent: String?,
        checkContent: CheckContent?,
    ) {
        HLog.i(
            HTag.TAG,
            "更新议论文批改结果 workId: ${workId}; workStateId: $workStateId; checkUserId: ${checkUserId}; jobStatus: ${checkContent?.jobStatus}"
        )
        checkContent ?: return
        val checkDetail = if (checkUserId > 0) {
            compositionLocalDataSource.queryCompositionTeacherCheckDetail(
                workId = workId, workStateId = workStateId
            )
        } else {
            compositionLocalDataSource.queryCompositionAiCheckDetail(
                workId = workId, workStateId = workStateId
            )
        }
        compositionLocalDataSource.updateCompositionCheckDetail(
            CompositionCheckDetailPo(
                id = checkDetail?.id,
                jobStatus = checkContent.jobStatus ?: JobStatus.NONE.value,
                message = checkContent.message,
                workId = workId,
                workStateId = workStateId,
                checkUserId = checkUserId,
                ocrTitle = compositionTitle,
                ocrContent = compositionContent,
                comprehensiveJudge = JsonUtil.objToJson(checkContent.aiJudgeResult?.comprehensiveJudge),
                adviceList = JsonUtil.objToJson(checkContent.aiJudgeResult?.adviceList),
                allusionList = JsonUtil.objToJson(checkContent.allusionResult?.allusionList),
                score = checkContent.scoreResult?.score ?: -1,
                updateTime = System.currentTimeMillis()
            )
        )
    }

    // ----------------------------------------------------------------------
    fun queryAiCheckDetail(
        workId: Long,
        workStateId: Long,
        isSubmitted: Boolean,
    ): CompositionCheckDetail? {
        return if (isSubmitted) {
            queryTeacherCheckDetail(
                workId = workId,
                workStateId = workStateId
            )
        } else {
            queryAiFirstCheckDetail(
                workId = workId,
                workStateId = workStateId
            )
        }
    }

    fun queryAiFirstCheckDetail(workId: Long, workStateId: Long): CompositionCheckDetail? {
        HLog.i(HTag.TAG, "查询首次作答批改结果: ${workId}_$workStateId")
        return compositionLocalDataSource.queryCompositionAiCheckDetail(
            workId = workId, workStateId = workStateId
        )?.asVo()
    }

    private fun queryTeacherCheckDetail(workId: Long, workStateId: Long): CompositionCheckDetail? {
        HLog.i(HTag.TAG, "查询二次作答批改结果: ${workId}_$workStateId")
        return compositionLocalDataSource.queryCompositionTeacherCheckDetail(
            workId = workId, workStateId = workStateId
        )?.asVo()
    }


    // ----------------------------------------------------------------------

//    fun queryCompositionWorkInfo(workId: Long): WorkDetail? {
//        return workLocalDataSource.queryWorkDetailById(workId = workId)
//        return null
//    }

    fun queryCompositionMaterials(workId: Long): List<TaskBean> {
//        return materialLocalDataSource.queryTaskList(workId = workId).map {
//            it.asTask()
//        }
        return emptyList()
    }

    // ----------------------------------------------------------------------
//    /**
//     * 首次提交
//     */
//    suspend fun requestJudgeJobAdd(
//        workId: Long,
//        schoolworkStateId: Long,
//        imageUrlList: List<String>,
//    ): BaseHttpResult<Any> {
//        val result = remoteDataSource.requestJudgeJobAdd(schoolworkStateId, imageUrlList)
//        if (result.isSuccess || result.code == BusinessErrorCode.Submitted) {
//            HLog.i(HTag.TAG, "议论文首次提交成功，删除本地历史数据：ocr, 点拨等")
//            compositionLocalDataSource.deleteCompositionPractice(
//                workId = workId, workStateId = schoolworkStateId
//            )
////            workLocalDataSource.clearWorkErrorMessage(
////                workId = workId,
////                workStateId = schoolworkStateId,
////            )
//        }
//        return result
//    }

    fun queryCompositionPracticeInfo(workId: Long, workStateId: Long): CompositionPractice? {
        return compositionLocalDataSource.queryCompositionPractice(
            workId = workId, workStateId = workStateId
        )?.asVo()
    }

    fun updateCompositionPracticeInfo(
        workId: Long,
        workStateId: Long,
        title: String?,
        content: String?,
    ) {
        HLog.d(
            HTag.TAG,
            "更新作文二次作答信息: workId: $workId; workStateId: $workStateId, $title, $content"
        )
        return compositionLocalDataSource.updateCompositionPractice(
            workId = workId, workStateId = workStateId, title = title, content = content
        )
    }

    suspend fun secondSubmit(workId: Long, workStateId: Long): Boolean {
        val localCompositionPractice = compositionLocalDataSource.queryCompositionPractice(
            workId = workId, workStateId = workStateId
        )
        val aiCheckDetail = compositionLocalDataSource.queryCompositionAiCheckDetail(
            workId = workId, workStateId = workStateId
        )
        val localWorkState =
            workLocalDataSource.queryWorkState(workId = workId, workStateId = workStateId)
        if (aiCheckDetail == null || localCompositionPractice == null || localWorkState == null) {
            HLog.w(HTag.TAG, "作文作业信息缺失，无法提交，请重新进入!")
            return false
        }
        val ocrTitle = aiCheckDetail.ocrTitle
        val ocrContent = aiCheckDetail.ocrContent
        val secondPracticeTitle = localCompositionPractice.secondPracticeTitle
        val secondPracticeContent = localCompositionPractice.secondPracticeContent
        if (secondPracticeTitle.isNullOrEmpty()) {
            HLog.w(HTag.TAG, "标题不能为空")
            return false
        }
        if (secondPracticeContent.isNullOrEmpty()) {
            HLog.w(HTag.TAG, "未作答不允许提交")
            return false
        }
        val orcList = ArrayList<String>()
        orcList.add(ocrTitle ?: "")
        ocrContent?.jsonToList(String::class.java)?.let {
            orcList.addAll(it)
        }
        //
        val secondAnswerList = ArrayList<String>()
        secondAnswerList.add(secondPracticeTitle)
        secondPracticeContent.jsonToList(String::class.java)?.let {
            secondAnswerList.addAll(it)
        }
        val answerBean = CompositionSecondSubmitRequest.AnswerDetail(
            studentFirstAnswerDetail = JsonUtil.objToJson(orcList),
            studentSecondAnswerDetail = JsonUtil.objToJson(secondAnswerList),
            studentEditDetail = "{}",
        )
        //
        val submitTimeMillis = System.currentTimeMillis()
        val requestBean = CompositionSecondSubmitRequest(
            schoolworkId = workId,
            schoolworkStateId = workStateId,
            startTime = DateUtil.timeMillisToString(localWorkState.startTime),
            submitTime = DateUtil.timeMillisToString(submitTimeMillis),
            time = localWorkState.timeCounting,
            status = WorkStatus.ALL_SUBMITTED,
            studentCheckScore = localWorkState.studentCheckScore,
            answerList = listOf(answerBean),
        )
        HLog.i(HTag.TAG, "作文二次作答提交: ${JsonUtil.objToJson(requestBean)}")
        val result = remoteDataSource.submitStudentSecondPracticeInfo(requestBean)
        return if (result.isSuccess || result.code == BusinessErrorCode.Submitted) {
            workLocalDataSource.updateWorkState(
                localWorkState.copy(
                    state = WorkStatus.ALL_SUBMITTED,
                    submitTime = submitTimeMillis,
                )
            )
            true
        } else {
            ToastUtils.showShort("二次作答提交失败: ${result.message}")
            false
        }
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    fun queryCompositionWorkAnswerWithFlow(workId: Long): Flow<CompositionWorkAnswerPo?> {
        HLog.i(HTag.TAG, "加载学生已提交的作答情况: workId: $workId")
        return compositionLocalDataSource.queryCompositionWorkAnswerWithFlow(workId)
    }

    private fun updateCompositionWorkAnswer(
        workId: Long,
        workStateId: Long,
        studentAnswerInfo: StudentAnswerInfo?,
    ) {
        if (studentAnswerInfo == null) { // 无远程信息，本地不动
            return
        }
        HLog.i(HTag.TAG, "更新学生作答情况: workId: $workId; $studentAnswerInfo")
        val local = compositionLocalDataSource.queryCompositionWorkAnswer(workId)
        compositionLocalDataSource.updateCompositionWorkAnswer(
            studentAnswerInfo.asPo(
                id = local?.id, workId = workId
            )
        )
        val answerList = studentAnswerInfo.studentSecondAnswerDetail?.jsonToList(String::class.java)
        if (answerList.isNullOrEmpty()) { // 远程没有二次作答信息
            return
        }
        // 已提交，可能是换平板了，需要将二次作答的信息以远程为准
        val compositionPractice = compositionLocalDataSource.queryCompositionPractice(
            workId = workId, workStateId = workStateId
        )
        val remoteTitle = answerList.firstOrNull()
        val remoteContent = JsonUtil.objToJson(answerList.drop(1))
        if (compositionPractice?.secondPracticeTitle != remoteTitle || compositionPractice?.secondPracticeContent != remoteContent) {
            compositionLocalDataSource.updateCompositionPractice(
                CompositionPracticePo(
                    id = compositionPractice?.id,
                    workId = workId,
                    workStateId = workStateId,
                    secondPracticeTitle = answerList.firstOrNull(),
                    secondPracticeContent = JsonUtil.objToJson(answerList.drop(1)),
                )
            )
        }
    }

    fun updateCompositionStudentImages(
        workId: Long,
        imageUrls: List<String>?,
    ) {
        HLog.i(HTag.TAG, "更新学生作答图片: workId: $workId; ${imageUrls?.joinToString(";")}")
        // ----------------------------------------------------------------------
        val local = compositionLocalDataSource.queryCompositionWorkAnswer(workId)
        if (local == null) {
            compositionLocalDataSource.updateCompositionWorkAnswer(
                StudentAnswerInfo(imgInfoList = imageUrls).asPo(
                    workId = workId
                )
            )
        } else {
            compositionLocalDataSource.updateCompositionWorkAnswer(
                local.copy(
                    imageAnswerList = JsonUtil.objToJson(
                        imageUrls
                    )
                )
            )
        }
    }

    fun saveTempAnswerPictures(workId: Long, pictureAnswerInfo: List<PictureAnswerInfo>?) {
        compositionLocalDataSource.deleteCompositionTempImages(workId)
        if (!pictureAnswerInfo.isNullOrEmpty()) {
//            DBUtils.insertOrReplaceList(PictureAnswerInfo::class.java, pictureAnswerInfo)
        }
    }

    fun queryTempAnswerPictures(workId: Long): List<PictureAnswerInfo> {
//        return DBUtils.queryWhere(
//            PictureAnswerInfo::class.java, PictureAnswerInfoDao.Properties.WorkId.eq(workId)
//        ) ?: emptyList()
        return emptyList()
    }

    private fun updateTempAnswerPicturesWhenAiFailed(workId: Long) {
        val pictureAnswerInfoList = queryTempAnswerPictures(workId)
        if (pictureAnswerInfoList.isNotEmpty()) {
            return
        }
        HLog.i(
            HTag.TAG,
            "updateTempAnswerPicturesWhenAiFailed: $workId; AI识别失败，但是本地没有学生作答图片，数据被删除了，读取远程图片信息覆盖本地"
        )
        val remoteList =
            compositionLocalDataSource.queryCompositionWorkAnswer(workId)?.imageAnswerList?.jsonToList(
                String::class.java
            )?.mapIndexed { index, url ->
                PictureAnswerInfo().also { pictureAnswerInfo ->
                    pictureAnswerInfo.workId = workId
                    pictureAnswerInfo.path = null
                    pictureAnswerInfo.url = url
                    pictureAnswerInfo.index = index
                }
            } ?: emptyList()
        saveTempAnswerPictures(workId = workId, remoteList)
    }
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    /**
     * 点赞
     */
    suspend fun addFeedbackEvaluate(
        workId: Long,
        workStateId: Long,
        feedbackType: FeedbackType,
        articleType: ArticleType,
        evaluateType: EvaluateType,
    ): BaseHttpResult<Any> {
        val result = remoteDataSource.addFeedbackEvaluate(
            FeedbackEvaluateRequest.Add(
                schoolworkId = workId,
                schoolworkStateId = workStateId,
                feedbackType = feedbackType.value,
                articleType = articleType.value,
                userType = "student",
                evaluateType = evaluateType.value,
            )
        )
        if (result.isSuccess) {
            updateFeedbackEvaluate(
                workId = workId,
                feedbackType = feedbackType.value,
                articleType = articleType.value,
                userType = "student",
                evaluateType = evaluateType.value
            )
        }
        return result
    }

    /**
     * 取消赞
     */
    suspend fun cancelFeedbackEvaluate(
        workId: Long,
        workStateId: Long,
        feedbackType: FeedbackType,
        articleType: ArticleType,
    ): BaseHttpResult<Any> {
        val result = remoteDataSource.cancelFeedbackEvaluate(
            FeedbackEvaluateRequest.Delete(
                schoolworkId = workId,
                schoolworkStateId = workStateId,
                feedbackType = feedbackType.value,
                articleType = articleType.value,
                userType = "student",
            )
        )
        if (result.isSuccess) {
            updateFeedbackEvaluate(
                workId = workId,
                feedbackType = feedbackType.value,
                articleType = articleType.value,
                userType = "student",
                evaluateType = null
            )
        }
        return result
    }


    fun getStudentFeedbackEvaluate(
        workId: Long,
        feedbackType: FeedbackType,
        articleType: ArticleType,
    ): EvaluateType? {
        return getFeedbackEvaluate(
            workId = workId,
            feedbackType = feedbackType,
            articleType = articleType,
            userType = "student",
        )
    }

    fun getTeacherFeedbackEvaluate(
        workId: Long,
        feedbackType: FeedbackType,
        articleType: ArticleType,
    ): EvaluateType? {
        return getFeedbackEvaluate(
            workId = workId,
            feedbackType = feedbackType,
            articleType = articleType,
            userType = "teacher",
        )
    }

    private fun getFeedbackEvaluate(
        workId: Long,
        feedbackType: FeedbackType,
        articleType: ArticleType,
        userType: String,
    ): EvaluateType? {
        val type = feedbackMap["${userType}_${workId}_${feedbackType.value}_${articleType.value}"]
        return when (type) {
            EvaluateType.Like.value -> {
                EvaluateType.Like
            }

            EvaluateType.Dislike.value -> {
                EvaluateType.Dislike
            }

            else -> {
                null
            }
        }
    }

    private fun updateFeedbackEvaluate(
        workId: Long,
        feedbackType: String?,
        articleType: String?,
        userType: String?,
        evaluateType: String?,
    ) {
        feedbackMap["${userType}_${workId}_${feedbackType}_${articleType}"] = evaluateType
    }
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    /**
     * 重试AI批改，返回Job Failed 但是 OCR success 后调用, OCR失败走其他流程，返回首次作答触发
     */
    suspend fun retryAiFirstJudge(workId: Long, workStateId: Long): BaseHttpResult<Any> {
        return remoteDataSource.retryAiFirstJudge(workStateId = workStateId)
    }

    /**
     * 重试AI 二次批改，返回Job Failed
     */
    suspend fun retryAiSecondJudge(workId: Long, workStateId: Long): BaseHttpResult<Any> {
        return remoteDataSource.retryAiSecondJudge(workStateId = workStateId)
    }

    // ----------------------------------------------------------------------
    suspend fun checkAiStreamStatus(
        workId: Long,
        workStateId: Long,
    ): BaseHttpResult<CompositionStatusResponse> {
        return remoteDataSource.checkAiStreamStatus(workId = workId, workStateId = workStateId)
    }

    /**
     * 获取审题立意流
     */
    fun requestTopicAiStreamInfo(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
        aiStreamStatus: AiStreamStatus,
    ): Flow<AiStreamDetail?> {
        return when (aiStreamStatus) {
            AiStreamStatus.Ready -> {
                remoteDataSource.requestTopicAiStreamInfo(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }

            AiStreamStatus.Failed -> {
                remoteDataSource.retryTopicAiStream(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }

            else -> {
                remoteDataSource.requestTopicAiStreamHistory(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }
        }
    }

    /**
     * 请求首次作答AI流程数据
     */
    fun requestFirstAiStreamInfo(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
        aiStreamStatus: AiStreamStatus,
    ): Flow<AiStreamDetail?> {
        return when (aiStreamStatus) {
            AiStreamStatus.Ready -> {
                remoteDataSource.requestFirstContentAiStreamInfo(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }

            AiStreamStatus.Failed -> {
                remoteDataSource.retryFirstContentAiStream(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }

            else -> {
                remoteDataSource.requestFirstContentAiStreamHistory(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }
        }
    }

    /**
     * 请求二次作答AI流程数据
     */
    fun requestSecondAiStreamInfo(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
        aiStreamStatus: AiStreamStatus,
    ): Flow<AiStreamDetail?> {
        return when (aiStreamStatus) {
            AiStreamStatus.Ready -> {
                remoteDataSource.requestSecondContentAiStreamInfo(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }

            AiStreamStatus.Failed -> {
                remoteDataSource.retrySecondContentAiStream(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }

            else -> {
                remoteDataSource.requestSecondContentAiStreamHistory(
                    sseClient = sseClient, workId = workId, workStateId = workStateId
                )
            }
        }
    }

    // ----------------------------------------------------------------------
//    fun isTakePhotoBeginnerGuidanceRead(): Boolean {
//        return SpManager.isTakePhotoBeginnerGuidanceRead()
//    }

    fun markTakePhotoBeginnerGuidanceRead() {
//        SpManager.markTakePhotoBeginnerGuidanceRead()
    }

    suspend fun addBeginnerGuidance(): BaseHttpResult<Any> {
        return remoteDataSource.addBeginnerGuidance()
    }

    suspend fun getBeginnerGuidance(): BaseHttpResult<AiGuidance> {
        return remoteDataSource.getBeginnerGuidance()
    }
    // ----------------------------------------------------------------------

    suspend fun addComposition(
        createWorkBean: CreateWorkBean,
    ): BaseHttpResult<FirstSubmitResponse> {
        val result = remoteDataSource.createOrUpdateComposition(createWorkBean)
        if (result.isSuccess || result.code == BusinessErrorCode.Submitted) {
            val data = result.data
            val workId = data?.schoolworkId ?: -1L
            val workStateId = data?.schoolworkStateId ?: -1L
            HLog.i(HTag.TAG, "作文创建/更新成功 data: $data")
            compositionLocalDataSource.deleteCompositionPractice(
                workId = workId,
                workStateId = workStateId
            )
            workLocalDataSource.clearWorkErrorMessage(
                workId = workId,
                workStateId = workStateId,
            )
        }
        return result
    }

    suspend fun getCompositionList(
        page: Int,
        pageSize: Int,
        subject: Int,
    ): BaseHttpResult<CompositionListWrapper> {
        return remoteDataSource.getCompositionList(
            page = page,
            pageSize = pageSize,
            subject = subject
        )
    }

    suspend fun deleteComposition(schoolworkStateId: Long): BaseHttpResult<Any> {
        // TODO，删除成功，需要清空本地相关数据
        return remoteDataSource.deleteComposition(schoolworkStateId = schoolworkStateId)
    }

    suspend fun getTextCorrectionInfo(): BaseHttpResult<JSONObject> {
        return remoteDataSource.getTextCorrectionInfo()
    }
}